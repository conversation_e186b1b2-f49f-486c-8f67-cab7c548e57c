#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票信号图片颜色分析工具
分析清仓、持仓、开仓信号的颜色特征
"""

import cv2
import numpy as np
from PIL import Image
import json
import os
from datetime import datetime

def analyze_image_colors(image_path, signal_type):
    """分析图片的颜色特征"""
    print(f"\n=== 分析 {signal_type} 信号图片: {image_path} ===")
    
    # 检查文件是否存在
    if not os.path.exists(image_path):
        print(f"错误：文件不存在 {image_path}")
        return None
    
    # 读取图片
    img = cv2.imread(image_path)
    if img is None:
        print(f"错误：无法读取图片 {image_path}")
        return None
    
    # 转换为RGB
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # 获取图片尺寸
    height, width, channels = img.shape
    print(f"图片尺寸: {width}x{height}")
    
    # 计算主要颜色
    pixels = img_rgb.reshape(-1, 3)
    
    # 统计颜色分布
    unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)
    
    # 按出现频率排序
    sorted_indices = np.argsort(counts)[::-1]
    top_colors = unique_colors[sorted_indices[:10]]  # 前10种颜色
    top_counts = counts[sorted_indices[:10]]
    
    print("主要颜色 (RGB值 - 出现次数 - 百分比):")
    color_info = []
    total_pixels = len(pixels)
    
    for i, (color, count) in enumerate(zip(top_colors, top_counts)):
        percentage = (count / total_pixels) * 100
        print(f"  {i+1}. RGB({color[0]}, {color[1]}, {color[2]}) - {count}次 - {percentage:.2f}%")
        
        color_info.append({
            'rank': i+1,
            'rgb': [int(color[0]), int(color[1]), int(color[2])],
            'count': int(count),
            'percentage': round(percentage, 2),
            'hex': f"#{color[0]:02x}{color[1]:02x}{color[2]:02x}"
        })
    
    # 计算平均颜色
    avg_color = np.mean(pixels, axis=0).astype(int)
    print(f"平均颜色: RGB({avg_color[0]}, {avg_color[1]}, {avg_color[2]})")
    
    # 分析颜色范围
    min_rgb = np.min(pixels, axis=0)
    max_rgb = np.max(pixels, axis=0)
    print(f"颜色范围: R({min_rgb[0]}-{max_rgb[0]}) G({min_rgb[1]}-{max_rgb[1]}) B({min_rgb[2]}-{max_rgb[2]})")
    
    # 计算HSV值
    img_hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    hsv_pixels = img_hsv.reshape(-1, 3)
    avg_hsv = np.mean(hsv_pixels, axis=0).astype(int)
    print(f"平均HSV: H({avg_hsv[0]}) S({avg_hsv[1]}) V({avg_hsv[2]})")
    
    return {
        'signal_type': signal_type,
        'image_path': image_path,
        'dimensions': {'width': width, 'height': height},
        'top_colors': color_info,
        'average_rgb': [int(avg_color[0]), int(avg_color[1]), int(avg_color[2])],
        'average_hsv': [int(avg_hsv[0]), int(avg_hsv[1]), int(avg_hsv[2])],
        'rgb_range': {
            'min': [int(min_rgb[0]), int(min_rgb[1]), int(min_rgb[2])],
            'max': [int(max_rgb[0]), int(max_rgb[1]), int(max_rgb[2])]
        }
    }

def compare_colors(color1, color2):
    """计算两个RGB颜色的欧几里得距离"""
    return np.sqrt(sum((c1 - c2) ** 2 for c1, c2 in zip(color1, color2)))

def analyze_color_similarity(results):
    """分析颜色相似性"""
    print("\n=== 颜色相似性分析 ===")
    
    signal_types = list(results.keys())
    
    for i in range(len(signal_types)):
        for j in range(i+1, len(signal_types)):
            type1, type2 = signal_types[i], signal_types[j]
            color1 = results[type1]['average_rgb']
            color2 = results[type2]['average_rgb']
            
            distance = compare_colors(color1, color2)
            print(f"{type1} vs {type2}: 颜色距离 = {distance:.2f}")
            
            if distance < 50:
                print(f"  ⚠️  警告：{type1}和{type2}颜色非常相似！")
            elif distance < 100:
                print(f"  ⚠️  注意：{type1}和{type2}颜色较为相似")

def main():
    """主函数"""
    print("股票信号颜色分析工具")
    print("=" * 50)
    
    # 图片路径
    image_paths = {
        '清仓信号': 'screenshots/qc2.png',
        '持仓信号': 'screenshots/cc2.png', 
        '开仓信号': 'screenshots/kc2.png'
    }
    
    results = {}
    
    # 分析每张图片
    for signal_type, image_path in image_paths.items():
        result = analyze_image_colors(image_path, signal_type)
        if result:
            results[signal_type] = result
    
    # 颜色相似性分析
    if len(results) >= 2:
        analyze_color_similarity(results)
    
    # 保存分析结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"signal_color_analysis_{timestamp}.json"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n分析结果已保存到: {output_file}")
    
    # 生成识别建议
    print("\n=== 颜色识别建议 ===")
    
    if '清仓信号' in results:
        qc_color = results['清仓信号']['average_rgb']
        print(f"清仓信号 (平均RGB): {qc_color}")
    
    if '持仓信号' in results and '开仓信号' in results:
        cc_color = results['持仓信号']['average_rgb']
        kc_color = results['开仓信号']['average_rgb']
        
        print(f"持仓信号 (平均RGB): {cc_color}")
        print(f"开仓信号 (平均RGB): {kc_color}")
        
        distance = compare_colors(cc_color, kc_color)
        print(f"持仓与开仓颜色距离: {distance:.2f}")
        
        if distance < 50:
            print("\n⚠️  持仓和开仓信号颜色极其相似，建议采用以下策略：")
            print("1. 结合OCR文字识别")
            print("2. 分析图像纹理特征")
            print("3. 使用更精细的HSV颜色空间分析")
            print("4. 考虑图像的空间位置信息")

if __name__ == "__main__":
    main()
